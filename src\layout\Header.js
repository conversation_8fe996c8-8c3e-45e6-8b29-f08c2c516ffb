import React, { useState, useEffect, useRef } from 'react';
import { Link } from 'react-router-dom';
import menu1 from './../imgs/menu1.png';
import menu2 from './../imgs/menu2.png';
import menu3 from './../imgs/menu3.png';
import menu4 from './../imgs/menu4.png';
import menu5 from './../imgs/menu5.png';
const Header = () => {
  const [showCatalogDropdown, setShowCatalogDropdown] = useState(false);
  const dropdownRef = useRef(null);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setShowCatalogDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const headerStyle = {
    backgroundColor: '#CDFF9A',
    padding: '15px 50px',
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    position: 'relative',
    zIndex: 100
  };

  const logoStyle = {
    fontSize: '24px',
    fontWeight: 'bold',
    color: '#000000',
    textDecoration: 'none',
    display: 'flex',
    alignItems: 'center'
  };

  const logoTextStyle = {
    display: 'flex',
    flexDirection: 'column',
    lineHeight: '1'
  };

  const navStyle = {
    display: 'flex',
    listStyle: 'none',
    margin: 0,
    padding: 0,
    gap: '30px'
  };

  const navLinkStyle = {
    textDecoration: 'none',
    color: '#000000',
    fontSize: '14px',
    fontWeight: '500',
    textTransform: 'uppercase',
    letterSpacing: '1px',
    transition: 'color 0.3s ease'
  };

  const navLinkHoverStyle = {
    color: '#ffffff'
  };

  const iconsStyle = {
    display: 'flex',
    gap: '15px',
    alignItems: 'center'
  };

  const iconStyle = {
    width: '20px',
    height: '20px',
    cursor: 'pointer',
    transition: 'transform 0.3s ease'
  };

  const dropdownMenuStyle = {
    position: 'fixed',
    top: '64px', // Chiều cao của header (padding + font size)
    left: '0',
    right: '0',
    backgroundColor: '#ffffff',
    width: '100vw',
    boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
    zIndex: 99,
    padding: '40px 50px',
    display: showCatalogDropdown ? 'block' : 'none'
  };

  const dropdownContentStyle = {
    display: 'flex',
    maxWidth: '1200px',
    margin: '0 auto',
    gap: '60px'
  };

  const categoryColumnStyle = {
    width: '200px'
  };

  const categoryTitleStyle = {
    fontSize: '14px',
    fontWeight: 'bold',
    color: '#333',
    marginBottom: '20px',
    textTransform: 'uppercase',
    letterSpacing: '1px',
    cursor: 'pointer',
    padding: '10px 0',
    borderBottom: '1px solid #eee'
  };

  const categoryItemsStyle = {
    display: 'grid',
    gridTemplateColumns: 'repeat(4, 1fr)',
    gap: '30px',
    flex: 1
  };

  const categoryItemStyle = {
    textAlign: 'center',
    textDecoration: 'none',
    color: '#333',
    transition: 'transform 0.3s ease'
  };

  const categoryImageStyle = {
    width: '150px',
    height: '150px',
    borderRadius: '8px',
    marginBottom: '15px',
    objectFit: 'cover',
    transition: 'transform 0.3s ease'
  };

  const categoryLabelStyle = {
    fontSize: '14px',
    fontWeight: '500',
    textTransform: 'uppercase',
    letterSpacing: '0.5px',
    color: '#333'
  };

  return (
    <>
    <header style={headerStyle}>
      {/* Logo */}
      <Link to="/" style={logoStyle}>
        <div style={logoTextStyle}>
          <span>HA</span>
          <span>SPORT</span>
        </div>
      </Link>

      {/* Navigation */}
      <nav>
        <ul style={navStyle}>
          <li>
            <Link 
              to="/" 
              style={navLinkStyle}
              onMouseEnter={(e) => e.target.style.color = navLinkHoverStyle.color}
              onMouseLeave={(e) => e.target.style.color = navLinkStyle.color}
            >
              HOME
            </Link>
          </li>
          <li>
            <div
              style={{
                ...navLinkStyle,
                cursor: 'pointer',
                userSelect: 'none'
              }}
              onClick={() => setShowCatalogDropdown(!showCatalogDropdown)}
              onMouseEnter={(e) => e.target.style.color = navLinkHoverStyle.color}
              onMouseLeave={(e) => e.target.style.color = navLinkStyle.color}
            >
              CATALOGS
            </div>
          </li>
          <li>
            <Link 
              to="/blog" 
              style={navLinkStyle}
              onMouseEnter={(e) => e.target.style.color = navLinkHoverStyle.color}
              onMouseLeave={(e) => e.target.style.color = navLinkStyle.color}
            >
              BLOG
            </Link>
          </li>
          <li>
            <Link 
              to="/shop" 
              style={navLinkStyle}
              onMouseEnter={(e) => e.target.style.color = navLinkHoverStyle.color}
              onMouseLeave={(e) => e.target.style.color = navLinkStyle.color}
            >
              SHOP
            </Link>
          </li>
          <li>
            <Link 
              to="/contact" 
              style={navLinkStyle}
              onMouseEnter={(e) => e.target.style.color = navLinkHoverStyle.color}
              onMouseLeave={(e) => e.target.style.color = navLinkStyle.color}
            >
              CONTACT US
            </Link>
          </li>
        </ul>
      </nav>

      {/* Icons */}
      <div style={iconsStyle}>
        {/* Search Icon */}
        <svg style={iconStyle} viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <circle cx="11" cy="11" r="8"></circle>
          <path d="m21 21-4.35-4.35"></path>
        </svg>
        
        {/* Heart Icon */}
        <svg style={iconStyle} viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
        </svg>
        
        {/* User Icon */}
        <svg style={iconStyle} viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
          <circle cx="12" cy="7" r="4"></circle>
        </svg>
        
        {/* Cart Icon */}
        <svg style={iconStyle} viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <circle cx="9" cy="21" r="1"></circle>
          <circle cx="20" cy="21" r="1"></circle>
          <path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path>
        </svg>
      </div>
    </header>

    {/* Dropdown Menu - Outside header */}
    <div style={dropdownMenuStyle} ref={dropdownRef}>
      <div style={dropdownContentStyle}>
        {/* Left Column - Categories */}
        <div style={categoryColumnStyle}>
          <div style={categoryTitleStyle}>Activity</div>
          <div style={categoryTitleStyle}>Equipment</div>
          <div style={categoryTitleStyle}>Women's</div>
          <div style={categoryTitleStyle}>Men's</div>
        </div>

        {/* Right Column - Product Images */}
        <div style={categoryItemsStyle}>
          <Link to="/catalog/boots" style={categoryItemStyle}>
            <img
              src={menu1}
              alt="Boots"
              style={categoryImageStyle}
              onMouseEnter={(e) => e.target.style.transform = 'scale(1.05)'}
              onMouseLeave={(e) => e.target.style.transform = 'scale(1)'}
            />
            <div style={categoryLabelStyle}>Boots</div>
          </Link>

          <Link to="/catalog/helmets" style={categoryItemStyle}>
            <img
              src={menu2}
              alt="Helmets"
              style={categoryImageStyle}
              onMouseEnter={(e) => e.target.style.transform = 'scale(1.05)'}
              onMouseLeave={(e) => e.target.style.transform = 'scale(1)'}
            />
            <div style={categoryLabelStyle}>Helmets</div>
          </Link>

          <Link to="/catalog/goggles" style={categoryItemStyle}>
            <img
              src={menu3}
              alt="Goggles"
              style={categoryImageStyle}
              onMouseEnter={(e) => e.target.style.transform = 'scale(1.05)'}
              onMouseLeave={(e) => e.target.style.transform = 'scale(1)'}
            />
            <div style={categoryLabelStyle}>Goggles</div>
          </Link>

          <Link to="/catalog/binding" style={categoryItemStyle}>
            <img
              src={menu4}
              alt="Binding"
              style={categoryImageStyle}
              onMouseEnter={(e) => e.target.style.transform = 'scale(1.05)'}
              onMouseLeave={(e) => e.target.style.transform = 'scale(1)'}
            />
            <div style={categoryLabelStyle}>Binding</div>
          </Link>
          <Link to="/catalog/binding" style={categoryItemStyle}>
            <img
              src={menu4}
              alt="Binding"
              style={categoryImageStyle}
              onMouseEnter={(e) => e.target.style.transform = 'scale(1.05)'}
              onMouseLeave={(e) => e.target.style.transform = 'scale(1)'}
            />
            <div style={categoryLabelStyle}>Binding</div>
          </Link>
        </div>
      </div>
    </div>
    </>
  );
};

export default Header;
